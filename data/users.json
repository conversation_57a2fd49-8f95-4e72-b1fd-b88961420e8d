[{"id": 1, "name": "<PERSON><PERSON>", "title": "Web3 Engineer", "avatar": "/avatars/tobi.png", "type": "freelancer", "email": "<EMAIL>", "address": "1 Remote Lane, Virtual City, Country"}, {"id": 2, "name": "<PERSON>", "title": "Graphic Designer", "avatar": "/avatars/jenny.png", "type": "freelancer", "email": "<EMAIL>", "address": "2 Remote Lane, Virtual City, Country"}, {"id": 3, "name": "<PERSON><PERSON>", "title": "UX/UI Designer", "avatar": "/avatars/geeko.png", "type": "freelancer", "email": "<EMAIL>", "address": "3 Remote Lane, Virtual City, Country"}, {"id": 4, "name": "<PERSON><PERSON>", "title": "Market Researcher", "avatar": "/avatars/muna.png", "type": "freelancer", "email": "<EMAIL>", "address": "4 Remote Lane, Virtual City, Country"}, {"id": 5, "name": "<PERSON>", "title": "Creative Writer", "avatar": "/avatars/shade.png", "type": "freelancer", "email": "<EMAIL>", "address": "5 Remote Lane, Virtual City, Country"}, {"id": 6, "name": "<PERSON><PERSON>", "title": "Video Editor", "avatar": "/avatars/kore.png", "type": "freelancer", "email": "<EMAIL>", "address": "6 Remote Lane, Virtual City, Country"}, {"id": 7, "name": "<PERSON><PERSON>", "title": "Director of Photography", "avatar": "/avatars/amara.png", "type": "freelancer", "email": "<EMAIL>", "address": "7 Remote Lane, Virtual City, Country"}, {"id": 8, "name": "<PERSON><PERSON><PERSON>", "title": "Fashion Designer", "avatar": "/avatars/kwame.png", "type": "freelancer", "email": "<EMAIL>", "address": "8 Remote Lane, Virtual City, Country"}, {"id": 9, "name": "<PERSON><PERSON>", "title": "Event Producer", "avatar": "/avatars/nia.png", "type": "freelancer", "email": "<EMAIL>", "address": "9 Remote Lane, Virtual City, Country"}, {"id": 10, "name": "<PERSON>", "title": "Print Specialist", "avatar": "/avatars/tai.png", "type": "freelancer", "email": "<EMAIL>", "address": "10 Remote Lane, Virtual City, Country"}, {"id": 11, "name": "<PERSON><PERSON>", "title": "Frontend Developer", "avatar": "/avatars/riya.png", "type": "freelancer", "email": "<EMAIL>", "address": "11 Remote Lane, Virtual City, Country"}, {"id": 12, "name": "<PERSON>", "title": "Brand Strategist", "avatar": "/avatars/pierre.png", "type": "freelancer", "email": "<EMAIL>", "address": "12 Remote Lane, Virtual City, Country"}, {"id": 13, "name": "<PERSON>", "title": "Motion Designer", "avatar": "/avatars/hans.png", "type": "freelancer", "email": "<EMAIL>", "address": "13 Remote Lane, Virtual City, Country"}, {"id": 14, "name": "<PERSON>", "title": "DevOps Engineer", "avatar": "/avatars/liam.png", "type": "freelancer", "email": "<EMAIL>", "address": "14 Remote Lane, Virtual City, Country"}, {"id": 15, "name": "<PERSON><PERSON>", "title": "Research Analyst", "avatar": "/avatars/asha.png", "type": "freelancer", "email": "<EMAIL>", "address": "15 Remote Lane, Virtual City, Country"}, {"id": 16, "name": "<PERSON>", "title": "Backend Developer", "avatar": "/avatars/daniel.png", "type": "freelancer", "email": "<EMAIL>", "address": "16 Remote Lane, Virtual City, Country"}, {"id": 17, "name": "Indah Putri", "title": "Virtual Assistant", "avatar": "/avatars/indah.png", "type": "freelancer", "email": "<EMAIL>", "address": "17 Remote Lane, Virtual City, Country"}, {"id": 18, "name": "<PERSON>", "title": "Content Writer", "avatar": "/avatars/elise.png", "type": "freelancer", "email": "<EMAIL>", "address": "18 Remote Lane, Virtual City, Country"}, {"id": 19, "name": "<PERSON>", "title": "Cloud Architect", "avatar": "/avatars/markus.png", "type": "freelancer", "email": "<EMAIL>", "address": "19 Remote Lane, Virtual City, Country"}, {"id": 20, "name": "<PERSON><PERSON>", "title": "Customer Success Manager", "avatar": "/avatars/anika.png", "type": "freelancer", "email": "<EMAIL>", "address": "20 Remote Lane, Virtual City, Country"}, {"id": 21, "name": "<PERSON>", "title": "Mobile Developer", "avatar": "/avatars/sarah.png", "type": "freelancer", "email": "<EMAIL>", "address": "21 Remote Lane, Virtual City, Country"}, {"id": 22, "name": "<PERSON>", "title": "App Developer", "avatar": "/avatars/miguel.png", "type": "freelancer", "email": "<EMAIL>", "address": "22 Remote Lane, Virtual City, Country"}, {"id": 23, "name": "Emeka Okafor", "title": "Python Developer", "avatar": "/avatars/emeka.png", "type": "freelancer", "email": "<EMAIL>", "address": "23 Remote Lane, Virtual City, Country"}, {"id": 24, "name": "<PERSON>", "title": "Shorts Video Creator", "avatar": "/avatars/hannah.png", "type": "freelancer", "email": "<EMAIL>", "address": "24 Remote Lane, Virtual City, Country"}, {"id": 25, "name": "<PERSON>", "title": "Hardware Engineer", "avatar": "/avatars/lucas.png", "type": "freelancer", "email": "<EMAIL>", "address": "25 Remote Lane, Virtual City, Country"}, {"id": 26, "name": "<PERSON>", "title": "Print Designer", "avatar": "/avatars/nadia.png", "type": "freelancer", "email": "<EMAIL>", "address": "26 Remote Lane, Virtual City, Country"}, {"id": 27, "name": "<PERSON><PERSON>", "title": "Stage Designer", "avatar": "/avatars/olu.png", "type": "freelancer", "email": "<EMAIL>", "address": "27 Remote Lane, Virtual City, Country"}, {"id": 28, "name": "Sofia Rossi", "title": "Poet", "avatar": "/avatars/sofia.png", "type": "freelancer", "email": "<EMAIL>", "address": "28 Remote Lane, Virtual City, Country"}, {"id": 29, "name": "<PERSON>", "title": "Full Stack Developer", "avatar": "/avatars/isaac.png", "type": "freelancer", "email": "<EMAIL>", "address": "29 Remote Lane, Virtual City, Country"}, {"id": 30, "name": "<PERSON>", "title": "Admin Support Specialist", "avatar": "/avatars/grace.png", "type": "freelancer", "email": "<EMAIL>", "address": "30 Remote Lane, Virtual City, Country"}, {"id": 31, "name": "<PERSON><PERSON><PERSON>", "title": "Creative Producer", "avatar": "/avatars/margsate-flether.png", "type": "freelancer", "email": "<EMAIL>", "address": "31 Remote Lane, Virtual City, Country", "username": "margsate", "password": "testpass", "about": "I am a user interface designer with 5+ years of experience designing complex digital experiences with human-centered design for startups. I've worked inside design and programming teams which gives me a broader vision in a graphic and technological context. I am known for always creating products that customers love with excellent interface, interactions (Interaction Design), micro-interactions, sketching, prototyping (Figma) and construction of design systems.", "socialLinks": [{"platform": "linkedin", "url": "https://www.linkedin.com/in/margsate-flether"}, {"platform": "behance", "url": "https://www.behance.net/margsate"}, {"platform": "dribbble", "url": "https://dribbble.com/margsate"}, {"platform": "website", "url": "https://margsate.com"}], "workSamples": [{"title": "Rotundo Agency / Web Design", "skill": "UI", "tool": "Figma", "year": 2023, "link": "https://margsate.com/projects/rotundo"}, {"title": "Real Time Palette Generator", "skill": "UI", "tool": "Figma", "year": 2021, "link": "https://margsate.com/projects/palette"}, {"title": "Spider-Man 2 / Hero Design", "skill": "Branding", "tool": "Adobe XD", "year": 2023, "link": "https://margsate.com/projects/spiderman"}, {"title": "Mavin Records / Web Design", "skill": "UI", "tool": "Figma", "year": 2021, "link": "https://margsate.com/projects/mavin"}], "trend": 1.78}, {"id": 32, "name": "<PERSON><PERSON>", "title": "Product Manager", "avatar": "/avatars/neilsan.png", "type": "commissioner", "email": "<EMAIL>", "address": "Hidden Leaf, Land of Fire", "organizationId": 1, "username": "<PERSON><PERSON><PERSON>", "password": "testpass", "bio": "Experienced product manager with over 8 years in public sector digital transformation. Passionate about creating user-centered solutions that improve community services and enhance citizen engagement.", "rating": 4.8, "responsibilities": ["Project Management", "Budget Oversight", "Stakeholder Communication", "Quality Assurance", "Timeline Management", "Resource Allocation", "Risk Assessment", "Team Coordination"], "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/neilsan-mando"}]}, {"id": 33, "name": "<PERSON><PERSON><PERSON><PERSON>ly P<PERSON>", "title": "Brand Director", "avatar": "/avatars/priyaa.png", "type": "commissioner", "email": "<EMAIL>", "address": "Lagos, Nigeria", "organizationId": 2, "bio": "Creative brand strategist with a focus on authentic storytelling and cultural representation. Leading brand initiatives that connect with diverse audiences across Africa and beyond.", "rating": 4.6, "responsibilities": ["Brand Strategy", "Creative Direction", "Content Oversight", "Campaign Management", "Market Research", "Vendor Relations", "Budget Planning"], "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/tiruvellly-priya"}]}, {"id": 34, "name": "<PERSON><PERSON>", "title": "Startup Founder", "avatar": "/avatars/matte.png", "type": "commissioner", "email": "<EMAIL>", "address": "Toronto, Canada", "organizationId": 3, "username": "matte", "password": "testpass", "bio": "Serial entrepreneur focused on wellness technology and mental health solutions. Building products that make wellness accessible and sustainable for everyone.", "rating": 4.9, "responsibilities": ["Product Vision", "Strategic Planning", "Investor Relations", "Team Leadership", "Market Validation", "Partnership Development"], "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/matte-hannery"}]}, {"id": 35, "name": "<PERSON><PERSON>", "title": "Events Lead", "avatar": "/avatars/tilly.png", "type": "commissioner", "email": "<PERSON>y.bur<PERSON><PERSON>@example.com", "address": "Ottawa, Canada", "organizationId": 4, "bio": "Dynamic events professional specializing in large-scale corporate and cultural events. Expert in creating memorable experiences that bring communities together.", "rating": 4.7, "responsibilities": ["Event Planning", "Vendor Co<PERSON>", "Budget Management", "Logistics Oversight", "Client Relations", "Risk Management", "Team Supervision"], "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/till<PERSON>-b<PERSON><PERSON><PERSON>"}]}, {"id": 36, "name": "<PERSON>", "title": "UX Research Lead", "avatar": "/avatars/felix.png", "type": "commissioner", "email": "<EMAIL>", "address": "Nairobi, Kenya", "organizationId": 5, "bio": "User experience researcher passionate about inclusive design and accessibility. Leading research initiatives that ensure digital products work for everyone, regardless of ability.", "rating": 4.8, "responsibilities": ["Research Strategy", "User Testing", "Data Analysis", "Stakeholder Reporting", "Design Collaboration", "Accessibility Audits", "Team Mentoring"], "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/felix-damini"}]}, {"id": 37, "name": "<PERSON>", "title": "Marketing Director", "avatar": "/avatars/sarah.png", "type": "commissioner", "email": "<EMAIL>", "address": "San Francisco, USA", "organizationId": 6, "bio": "Marketing leader with expertise in tech product launches and developer community building. Passionate about creating authentic connections between technology and the people who use it.", "rating": 4.5, "responsibilities": ["Marketing Strategy", "Campaign Development", "Community Building", "Content Strategy", "Performance Analytics", "Brand Management", "Partnership Marketing"], "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/sarah-chen-marketing"}]}, {"id": 38, "name": "Dr. <PERSON>", "title": "Research Director", "avatar": "/avatars/michael.png", "type": "commissioner", "email": "<EMAIL>", "address": "Austin, USA", "organizationId": 7, "bio": "Research director with a PhD in Human-Computer Interaction, specializing in accessibility and inclusive design. Committed to making technology work for people with disabilities.", "rating": 4.9, "responsibilities": ["Research Leadership", "Grant Management", "Academic Partnerships", "Publication Oversight", "Team Development", "Policy Advocacy", "Technology Assessment"], "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/dr-micha<PERSON>-to<PERSON>s"}]}]